using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using MassStorageStableTestTool.Automation.GUI;
using MassStorageStableTestTool.Core.Common;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Enums;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.IO;
using FlaUI.UIA3.Patterns;
using FlaUI.Core.WindowsAPI;

namespace MassStorageStableTestTool.Automation.Controllers;

/// <summary>
/// H2testw GUI自动化控制器 - 重构版本，直接使用ILogger
/// </summary>
public class H2TestController : BaseTestToolController
{
    private readonly AutomationHelper _automationHelper;
    private readonly ILogger<H2TestController> _logger;
    private Process? _h2testProcess;
    private Window? _h2testWindow;
    private readonly string _instanceId;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">工具配置</param>
    /// <param name="automationHelper">自动化助手</param>
    /// <param name="logger">日志记录器</param>
    public H2TestController(
        TestToolConfig configuration,
        AutomationHelper automationHelper,
        ILogger<H2TestController> logger)
        : base(configuration)
    {
        _automationHelper = automationHelper ?? throw new ArgumentNullException(nameof(automationHelper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _instanceId = Guid.NewGuid().ToString("N")[..8]; // 8位唯一标识符

        _logger.LogInformation("H2TestController实例已创建，ID: {InstanceId}", _instanceId);
    }

    /// <summary>
    /// 工具名称
    /// </summary>
    public override string ToolName => "H2testw";

    /// <summary>
    /// 工具类型
    /// </summary>
    public override TestToolType ToolType => TestToolType.GUI;

    /// <summary>
    /// 执行具体的测试逻辑
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试结果</returns>
    protected override async Task<TestResult> ExecuteTestInternalAsync(
        TestConfiguration config,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        var result = new TestResult
        {
            ToolName = ToolName,
            StartTime = DateTime.Now
        };

        // 将目标驱动器信息存储在Data字典中
        result.SetPerformanceData("TargetDrive", config.TargetDrive);

        try
        {
            //格式化驱动器
            if (!await FormatDriveBeforeTestAsync(config, cancellationToken))
            {
                throw new Exception("格式化驱动器失败");
            }

            // 启动H2testw程序
            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "启动H2testw程序..." });
            if (!await StartH2TestAsync(cancellationToken))
            {
                throw new Exception("启动H2testw程序失败");
            }

            // 等待窗口出现 - 通过进程ID精确查找，避免多实例冲突
            progress?.Report(new ProgressEventArgs { Progress = 20, Status = "等待H2testw窗口..." });
            if (_h2testProcess != null)
            {
                _h2testWindow = _automationHelper.WaitForWindowByProcess(_h2testProcess.Id, "H2testw", _configuration.Timeouts.LaunchTimeout);
            }
            else
            {
                // 降级到标题查找（兼容性）
                _h2testWindow = _automationHelper.WaitForWindow("H2testw", _configuration.Timeouts.LaunchTimeout);
            }

            if (_h2testWindow == null)
            {
                throw new Exception("H2testw窗口未出现");
            }

            // 配置测试参数
            progress?.Report(new ProgressEventArgs { Progress = 30, Status = "配置测试参数..." });
            if (!await ConfigureTestParametersAsync(config, cancellationToken))
            {
                throw new Exception("配置测试参数失败");
            }

            // 开始测试
            progress?.Report(new ProgressEventArgs { Progress = 40, Status = "开始执行测试..." });
            if (!await StartTestAsync(cancellationToken))
            {
                throw new Exception("开始测试失败");
            }

            // 等待测试完成
            progress?.Report(new ProgressEventArgs { Progress = 50, Status = "等待测试完成..." });
            if (!await WaitForTestCompletionAsync(config, cancellationToken, progress))
            {
                throw new Exception("测试未在预期时间内完成");
            }

            // 解析测试结果
            progress?.Report(new ProgressEventArgs { Progress = 90, Status = "解析测试结果..." });
            await ParseTestResultsAsync(config, result);

            progress?.Report(new ProgressEventArgs { Progress = 100, Status = "测试完成" });
            result.Success = true;
        }
        catch (OperationCanceledException)
        {
            result.Cancel();
            _logger.LogInformation("H2testw测试被取消");
        }
        catch (Exception ex)
        {
            result.Complete(false, ex.Message);
            result.Exception = ex;
            _logger.LogError(ex, "H2testw测试失败: {Message}", ex.Message);
        }
        finally
        {
            await CleanupAsync();
        }

        result.EndTime = DateTime.Now;
        return result;
    }

    /// <summary>
    /// 启动H2testw程序
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功启动</returns>
    private async Task<bool> StartH2TestAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 将相对路径转换为绝对路径
            var absolutePath = Path.IsPathRooted(_configuration.ExecutablePath)
                ? _configuration.ExecutablePath
                : Path.GetFullPath(_configuration.ExecutablePath);

            _logger.LogInformation("启动H2testw程序: {ExecutablePath} (绝对路径: {AbsolutePath})",
                _configuration.ExecutablePath, absolutePath);

            // 验证文件是否存在
            if (!File.Exists(absolutePath))
            {
                _logger.LogError("H2testw可执行文件不存在: {AbsolutePath}", absolutePath);
                return false;
            }

            var workingDirectory = Path.GetDirectoryName(absolutePath);
            var startInfo = new ProcessStartInfo
            {
                FileName = absolutePath,
                UseShellExecute = false,
                WorkingDirectory = workingDirectory
            };

            _h2testProcess = Process.Start(startInfo);
            if (_h2testProcess == null)
            {
                _logger.LogError("无法启动H2testw进程");
                return false;
            }

            // 等待进程启动
            await Task.Delay(2000, cancellationToken);

            _logger.LogInformation("H2testw进程已启动，PID: {ProcessId}", _h2testProcess.Id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动H2testw程序失败: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 配置测试参数
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功配置</returns>
    private async Task<bool> ConfigureTestParametersAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            if (_h2testWindow == null || _h2testProcess == null)
                return false;

            _logger.LogInformation("配置H2testw测试参数");
            //切换英文界面
            var enlglishAutomationId = GetUIConfigValue("englishAutomationId", "1023");
            if (string.IsNullOrEmpty(enlglishAutomationId))
            {
                _logger.LogWarning("无法找到切换英文界面的按钮自动化ID配置");
                return false;
            }

            var success = await _automationHelper.FindAndClickButtonByAutomationId(_h2testWindow, enlglishAutomationId);
            if (!success)
            {
                _logger.LogWarning("无法点击切换英文界面按钮");
                return false;
            }

            // 选择目标驱动器
            var dirveSelectButton = GetUIConfigValue("SelectTargetAutoId", "1002");
            if (!string.IsNullOrEmpty(dirveSelectButton))
            {
                // 点击"Select target"按钮  
                success = await _automationHelper.FindAndClickButtonByAutomationId(_h2testWindow, dirveSelectButton, TimeSpan.FromSeconds(5));
                if (!success)
                {
                    _logger.LogWarning("无法点击选择目标驱动器按钮: {TargetDrive}", config.TargetDrive);
                    return false;
                }

                //找到点击按钮之后弹出的文件选择对话框
                var selectFolderWindow = await FindChildDialogAsync(_h2testProcess.Id, new[]
                {
                    "浏览文件夹",           // 中文系统
                    "Browse For Folder",   // 英文系统
                    "Select Folder",       // 可能的英文标题
                    "Choose Folder",       // 另一种可能
                    "文件夹选择",          // 另一种中文标题
                    ""                     // 空标题的对话框
                });

                if (selectFolderWindow == null)
                {
                    _logger.LogWarning("无法找到驱动器选择对话框，尝试查找所有子窗口");
                    // 如果找不到，列出所有可能的子窗口用于调试
                    await LogAllChildWindowsAsync(_h2testProcess.Id);
                    return false;
                }

                // 在文件选择对话框中输入目标驱动器路径
                var folderEditBoxAutoId = GetUIConfigValue("FolderEditBoxAutoId", "14148");
                if (string.IsNullOrEmpty(folderEditBoxAutoId))
                {
                    _logger.LogWarning("无法找到文件选择对话框中的编辑框自动化ID配置");
                    return false;
                }

                if (!_automationHelper.FindAndSetTextBoxByAutomationId(selectFolderWindow, folderEditBoxAutoId, config.TargetDrive))
                {
                    _logger.LogWarning("无法在文件选择对话框中输入目标驱动器路径: {TargetDrive}", config.TargetDrive);
                    return false;
                }

                //点击确定完成驱动器选择
                var confirmButtonAutoId = GetUIConfigValue("ConfirmButtonAutoId", "1");
                if (string.IsNullOrEmpty(confirmButtonAutoId))
                {
                    _logger.LogWarning("无法找到文件选择对话框中的确定按钮自动化ID配置");
                    return false;
                }

                success = await _automationHelper.FindAndClickButtonByAutomationId(selectFolderWindow, confirmButtonAutoId, TimeSpan.FromSeconds(5));
                if (!success)
                {
                    _logger.LogWarning("无法点击文件选择对话框中的确定按钮");
                    return false;
                }
            }

            // 设置测试全部空间选项
            var testAllSpaceValue = _configuration.DefaultParameters.GetValueOrDefault("TestAllSpace", true);
            var (shouldTestAll, testSizeMB) = ParseTestAllSpaceValue(testAllSpaceValue);

            if (shouldTestAll)
            {
                _logger.LogInformation("设置为测试全部空间");
                var AllSpaceRadioButtonAutoId = GetUIConfigValue("AllSpaceRadioButtonAutoId", "1000");

                if (string.IsNullOrEmpty(AllSpaceRadioButtonAutoId))
                {
                    _logger.LogWarning("无法找到测试全部空间选项的自动化ID配置");
                    return false;
                }

                success = await _automationHelper.FindAndClickButtonByAutomationId(_h2testWindow, AllSpaceRadioButtonAutoId, TimeSpan.FromMilliseconds(500));
                if (!success)
                {
                    _logger.LogWarning("无法点击测试全部空间选项");
                    return false;
                }
            }
            else if (testSizeMB > 0)
            {
                _logger.LogInformation("设置为测试指定大小: {TestSizeMB} MB", testSizeMB);
                //如果不是true则应该填入指定的大小,MB
                var SpecificSizeRadioButtonAutoId = GetUIConfigValue("SpecificSizeRadioButtonAutoId", "1001");
                if (string.IsNullOrEmpty(SpecificSizeRadioButtonAutoId))
                {
                    _logger.LogWarning("无法找到测试指定大小选项的自动化ID配置");
                    return false;
                }

                success = await _automationHelper.FindAndClickButtonByAutomationId(_h2testWindow, SpecificSizeRadioButtonAutoId, TimeSpan.FromMilliseconds(500));
                if (!success)
                {
                    _logger.LogWarning("无法点击测试指定大小选项");
                    return false;
                }

                var sizeTextBoxAutoId = GetUIConfigValue("SizeTextBoxAutoId", "1004");
                if(string.IsNullOrEmpty(sizeTextBoxAutoId))
                {
                    _logger.LogWarning("无法找到测试指定大小文本框的自动化ID配置");
                    return false;
                }

                if (!_automationHelper.FindAndSetTextBoxByAutomationId(_h2testWindow, sizeTextBoxAutoId, testSizeMB.ToString()))
                {
                    _logger.LogWarning("无法在测试指定大小文本框中输入指定大小");
                    return false;
                }
            }
            else
            {
                _logger.LogWarning("TestAllSpace 配置值无效，既不是 true 也不是有效的数字: {Value}", testAllSpaceValue);
                return false;
            }

            await Task.Delay(500, cancellationToken);
            _logger.LogInformation("H2testw参数配置完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "配置H2testw参数失败: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 开始测试
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功开始</returns>
    private async Task<bool> StartTestAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (_h2testWindow == null)
                return false;

            _logger.LogInformation("开始H2testw测试");

            // 点击"Write + Verify"按钮
            var writeVerifyButton = GetUIConfigValue("WriteVerifyButtonAutoId", "1006");
            if (!string.IsNullOrEmpty(writeVerifyButton))
            {
                var success = await _automationHelper.FindAndClickButtonByAutomationId(
                    _h2testWindow,
                    writeVerifyButton,
                    _configuration.Timeouts.ElementTimeout,
                    true);

                if (!success)
                {
                    // 尝试查找包含"Write"或"Verify"文本的按钮
                    var buttons = _h2testWindow.FindAllDescendants(cf => cf.ByControlType(ControlType.Button));

                    foreach (var button in buttons)
                    {
                        var buttonText = button.Name;
                        if (!string.IsNullOrEmpty(buttonText) &&
                            (buttonText.Contains("Write", StringComparison.OrdinalIgnoreCase) ||
                             buttonText.Contains("Verify", StringComparison.OrdinalIgnoreCase)))
                        {
                            await _automationHelper.InputSimulator.ClickElement(button);
                            success = true;
                            break;
                        }
                    }
                }

                //有可能弹出一个确认的子窗口关闭它
                var confirmationWindow = await FindChildDialogAsync(_h2testProcess.Id, new[]
                {
                    "H2testw",           // 中文系统
                    "H2testw",           // 英文系统
                    "",                  // 空标题的对话框
                    "确认",              // 确认对话框
                    "Confirm",           // 英文确认对话框
                    "Warning",           // 警告对话框
                    "警告"               // 中文警告对话框
                });

                if (confirmationWindow != null)
                {
                    _logger.LogInformation("找到确认窗口: {Title}，尝试关闭它", confirmationWindow.Title);
                    await _automationHelper.WindowManager.CloseWindow(confirmationWindow, smart: true);
                }

                if (!success)
                {
                    _logger.LogError("无法找到或点击开始测试按钮");
                    return false;
                }
            }

            await Task.Delay(1000, cancellationToken);
            _logger.LogInformation("H2testw测试已开始");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始H2testw测试失败: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 等待测试完成
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>是否在预期时间内完成</returns>
    private async Task<bool> WaitForTestCompletionAsync(
        TestConfiguration config, 
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        try
        {
            if (_h2testWindow == null || _h2testProcess == null)
                return false;

            //查找测试窗口，点击"Write + Verify"按钮后会弹出一个新的测试窗口
            var testWindowTitle = GetUIConfigValue("testWindowName", "H2testw | Progress");
            var testWindow = _automationHelper.WindowManager.FindChildWindowByTitleAsync(_h2testWindow, testWindowTitle, TimeSpan.FromSeconds(3));
            if (testWindow == null)
            {
                LogWarning("无法找到H2testw测试窗口");
                return false;
            }

            LogInfo("等待H2testw测试完成");

            //查找进度条
            var progressBarName = GetUIConfigValue("ProcessBarAutoId", "1010");
            if(string.IsNullOrEmpty(progressBarName))
            {
                LogWarning("无法找到进度条自动化ID配置");
                return false;
            }

            var progressBar = _automationHelper.ElementFinder.FindElementByAutomationId(testWindow, progressBarName, TimeSpan.FromSeconds(2));
            if (progressBar == null || !progressBar.IsAvailable)
            {
                LogWarning("无法找到进度条");
                return false;
            }

            //定期获取进度条的值
            var timeout = _configuration.Timeouts.TestTimeout;
            var endTime = DateTime.Now.Add(timeout);

            while ((timeout != TimeSpan.Zero || DateTime.Now < endTime) && !cancellationToken.IsCancellationRequested)
            {
                //获取进度条的LegacyIAccessible.Value值，这就是进度
                var legacyPattern = (LegacyIAccessiblePattern)progressBar.Patterns.LegacyIAccessible.Pattern;
                var progressValue = legacyPattern.Value.Value?.Trim() ?? string.Empty;
                // 移除可能的百分号和其他非数字字符
                progressValue = Regex.Replace(progressValue, @"[^0-9]", "");
                if (int.TryParse(progressValue, out var progressPercent))
                {
                    progress?.Report(new ProgressEventArgs
                    {
                        Progress = progressPercent,
                        Status = $"测试进行中... ({progressPercent}%)"
                    });
                }

                if (progressPercent >= 100)
                {
                    LogInfo("H2testw测试已完成");
                    return true;
                }

                await Task.Delay(5000, cancellationToken);
            }

            if (cancellationToken.IsCancellationRequested)
            {
                LogInfo("H2testw测试被取消");
                return false;
            }

            LogWarning($"H2testw测试在 {timeout.TotalMinutes} 分钟内未完成");
            return false;
        }
        catch (Exception ex)
        {
            LogError($"等待H2testw测试完成时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查测试是否完成
    /// </summary>
    /// <returns>是否完成</returns>
    private async Task<bool> CheckTestCompletionAsync()
    {
        try
        {
            if (_h2testWindow == null || _h2testProcess == null)
                return false;
            
            //查找测试窗口，点击"Write + Verify"按钮后会弹出一个新的测试窗口
            var testWindowTitle = GetUIConfigValue("testWindowName", "H2testw | Progress");
            var testWindow = _automationHelper.WaitForWindowByProcess(_h2testProcess.Id, testWindowTitle, _configuration.Timeouts.ElementTimeout);
            if (testWindow == null)
            {
                LogWarning("无法找到H2testw测试窗口");
                return false;
            }

            // 查找包含"finished"、"completed"、"done"等关键词的文本
            var textElements = testWindow.FindAllDescendants(cf => cf.ByControlType(ControlType.Text));

            foreach (var element in textElements)
            {
                var text = element.Name;
                if (!string.IsNullOrEmpty(text))
                {
                    if (text.Contains("finished", StringComparison.OrdinalIgnoreCase) ||
                        text.Contains("completed", StringComparison.OrdinalIgnoreCase) ||
                        text.Contains("done", StringComparison.OrdinalIgnoreCase) ||
                        text.Contains("Test finished", StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogInformation("发现完成标识: {Text}", text);
                        return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            LogWarning($"检查测试完成状态时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 解析测试结果
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="result">测试结果对象</param>
    /// <returns>解析任务</returns>
    private async Task ParseTestResultsAsync(TestConfiguration config, TestResult result)
    {
        try
        {
            if (_h2testWindow == null)
                return;

            _logger.LogInformation("解析H2testw测试结果");

            // 尝试截图保存结果
            var screenshotPath = _automationHelper.CaptureForDebugging(_h2testWindow, $"{Environment.UserName}_Driver({config.TargetDrive.TrimEnd(':', '\\', '/')})");
            if (!string.IsNullOrEmpty(screenshotPath))
            {
                result.OutputFiles.Add(screenshotPath);
            }

            // 查找并解析结果文本
            var textElements = _h2testWindow.FindAllDescendants(cf => cf.ByControlType(ControlType.Edit));

            var resultText = new List<string>();
            foreach (var element in textElements)
            {
                var text = element.Name;
                if (!string.IsNullOrEmpty(text))
                {
                    resultText.Add(text);
                }
            }

            var fullResultText = string.Join("\n", resultText);
            result.AddLog($"原始输出: {fullResultText}");

            // 使用配置的解析规则提取性能指标
            var parsingRules = _configuration.OutputParsing.ParsingRules;

            if (parsingRules.TryGetValue("WriteSpeed", out var writeSpeedPattern))
            {
                var match = Regex.Match(fullResultText, writeSpeedPattern, RegexOptions.IgnoreCase);
                if (match.Success && double.TryParse(match.Groups[1].Value, out var writeSpeed))
                {
                    result.SetPerformanceData("WriteSpeed", writeSpeed);
                    _logger.LogInformation("写入速度: {WriteSpeed} MB/s", writeSpeed);
                }
            }

            if (parsingRules.TryGetValue("ReadSpeed", out var readSpeedPattern))
            {
                var match = Regex.Match(fullResultText, readSpeedPattern, RegexOptions.IgnoreCase);
                if (match.Success && double.TryParse(match.Groups[1].Value, out var readSpeed))
                {
                    result.SetPerformanceData("ReadSpeed", readSpeed);
                    _logger.LogInformation("读取速度: {ReadSpeed} MB/s", readSpeed);
                }
            }

            if (parsingRules.TryGetValue("TestResult", out var testResultPattern))
            {
                var match = Regex.Match(fullResultText, testResultPattern, RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    var testStatus = match.Groups[1].Value.ToLowerInvariant();
                    result.Success = testStatus.Contains("finished") && !testStatus.Contains("failed");
                    _logger.LogInformation("测试状态: {TestStatus}", testStatus);
                }
            }

            if (parsingRules.TryGetValue("ErrorCount", out var errorCountPattern))
            {
                var match = Regex.Match(fullResultText, errorCountPattern, RegexOptions.IgnoreCase);
                if (match.Success && int.TryParse(match.Groups[1].Value, out var errorCount))
                {
                    result.SetPerformanceData("ErrorCount", errorCount);
                    if (errorCount > 0)
                    {
                        result.Success = false;
                        result.ErrorMessage = $"检测到 {errorCount} 个错误";
                    }
                    _logger.LogInformation("错误数量: {ErrorCount}", errorCount);
                }
            }

            // 设置测试结果摘要
            var writeSpeedValue = result.GetPerformanceData<double>("WriteSpeed");
            var readSpeedValue = result.GetPerformanceData<double>("ReadSpeed");
            var errorCountValue = result.GetPerformanceData<int>("ErrorCount");

            result.AddLog($"H2testw测试完成 - 写入速度: {writeSpeedValue:F2} MB/s, " +
                         $"读取速度: {readSpeedValue:F2} MB/s, " +
                         $"错误数量: {errorCountValue}");

            _logger.LogInformation("H2testw结果解析完成");
        }
        catch (Exception ex)
        {
            LogError($"解析H2testw测试结果失败: {ex.Message}");
            result.ErrorMessage = $"结果解析失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    /// <returns>清理任务</returns>
    private async Task CleanupAsync()
    {
        try
        {
            LogInfo("清理H2testw资源");

            // 关闭窗口
            if (_h2testWindow != null)
            {
                try
                {
                    //尝试找到测试窗口
                    if (_h2testProcess != null)
                    {
                        var testWindowTitle = GetUIConfigValue("testWindowName", "H2testw | Progress");
                        var testWindow = _automationHelper.WaitForWindowByProcess(_h2testProcess.Id, testWindowTitle, _configuration.Timeouts.ElementTimeout);
                        if (testWindow != null)
                        {
                            //关闭testWindow
                            await _automationHelper.WindowManager.CloseWindow(testWindow, false, true);
                            {
                                var closed = _automationHelper.WindowManager.WaitForWindowClosed(
                                    testWindow,
                                    _configuration.Timeouts.ShutdownTimeout);
                                if (!closed)
                                {
                                    LogWarning("测试窗口未正常关闭，尝试强制关闭");
                                    await _automationHelper.WindowManager.CloseWindow(testWindow, true);
                                }
                            }
                        }
                    }

                    await _automationHelper.WindowManager.CloseWindow(_h2testWindow, false, true);

                    {
                        // 等待窗口关闭
                        var closed = _automationHelper.WindowManager.WaitForWindowClosed(
                        _h2testWindow,
                        _configuration.Timeouts.ShutdownTimeout);

                        if (!closed)
                        {
                            LogWarning("窗口未正常关闭，尝试强制关闭");
                            await _automationHelper.WindowManager.CloseWindow(_h2testWindow, true);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogWarning($"关闭H2testw窗口时出错: {ex.Message}");
                }
                finally
                {
                    _h2testWindow = null;
                }
            }

            // 终止进程
            if (_h2testProcess != null && !_h2testProcess.HasExited)
            {
                try
                {
                    _h2testProcess.CloseMainWindow();

                    if (!_h2testProcess.WaitForExit(5000))
                    {
                        LogWarning("H2testw进程未正常退出，强制终止");
                        _h2testProcess.Kill();
                        _h2testProcess.WaitForExit(5000);
                    }
                }
                catch (Exception ex)
                {
                    LogWarning($"终止H2testw进程时出错: {ex.Message}");
                }
                finally
                {
                    _h2testProcess?.Dispose();
                    _h2testProcess = null;
                }
            }

            await Task.Delay(1000);
            LogInfo("H2testw资源清理完成");
        }
        catch (Exception ex)
        {
            LogError($"清理H2testw资源时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 准备测试环境
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功准备</returns>
    public override async Task<bool> PrepareTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            LogInfo("准备H2testw测试环境");

            // 检查目标驱动器是否存在
            if (!Directory.Exists(config.TargetDrive))
            {
                LogError($"目标驱动器不存在: {config.TargetDrive}");
                return false;
            }

            // 检查驱动器可用空间
            var driveInfo = new System.IO.DriveInfo(config.TargetDrive);
            var availableSpace = driveInfo.AvailableFreeSpace / (1024 * 1024); // MB
            var requiredSpace = _configuration.MinimumDiskSpace;

            if (availableSpace < requiredSpace)
            {
                LogError($"驱动器可用空间不足: {availableSpace} MB < {requiredSpace} MB");
                return false;
            }

            LogInfo($"目标驱动器: {config.TargetDrive}, 可用空间: {availableSpace} MB");
            return true;
        }
        catch (Exception ex)
        {
            LogError($"准备H2testw测试环境失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 清理测试环境
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功清理</returns>
    public override async Task<bool> CleanupTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        await CleanupAsync();
        return true;
    }

    /// <summary>
    /// 停止正在运行的测试
    /// </summary>
    /// <returns>是否成功停止</returns>
    public override async Task<bool> StopTestAsync()
    {
        try
        {
            LogInfo("停止H2testw测试");

            if (_h2testProcess != null && !_h2testProcess.HasExited)
            {
                _h2testProcess.Kill();
                await _h2testProcess.WaitForExitAsync();
                LogInfo("H2testw测试已停止");
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            LogError($"停止H2testw测试失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 查找子对话框
    /// </summary>
    /// <param name="parentProcessId">父进程ID</param>
    /// <param name="possibleTitles">可能的窗口标题</param>
    /// <returns>找到的子对话框窗口</returns>
    private async Task<Window?> FindChildDialogAsync(int parentProcessId, string[] possibleTitles)
    {
        var timeout = _configuration.Timeouts.ElementTimeout;
        var endTime = DateTime.Now.Add(timeout);

        while (DateTime.Now < endTime)
        {
            try
            {
                // 方法1：按进程ID和标题查找
                foreach (var title in possibleTitles)
                {
                    var window = _automationHelper.WaitForWindowByProcess(parentProcessId, title, TimeSpan.FromMilliseconds(500));
                    if (window != null)
                    {
                        _logger.LogInformation("找到子对话框: {Title}", window.Title);
                        return window;
                    }
                }

                // 方法2：查找所有顶级窗口，寻找可能的对话框
                var allWindows = GetAllTopLevelWindows();
                foreach (var window in allWindows)
                {
                    // 检查是否是对话框类型的窗口
                    if (IsDialogWindow(window))
                    {
                        _logger.LogInformation("找到对话框类型的窗口: {Title} (ClassName: {ClassName})",
                            window.Title, window.ClassName);
                        return window;
                    }
                }

                await Task.Delay(500);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "查找子对话框时出错");
                await Task.Delay(500);
            }
        }

        return null;
    }

    /// <summary>
    /// 获取所有顶级窗口
    /// </summary>
    /// <returns>窗口列表</returns>
    private List<Window> GetAllTopLevelWindows()
    {
        try
        {
            var windows = new List<Window>();
            var automation = new FlaUI.UIA3.UIA3Automation();
            var desktop = automation.GetDesktop();
            var allWindows = desktop.FindAllChildren(cf => cf.ByControlType(FlaUI.Core.Definitions.ControlType.Window));

            foreach (var window in allWindows)
            {
                try
                {
                    var win = window.AsWindow();
                    if (win != null)
                    {
                        windows.Add(win);
                    }
                }
                catch
                {
                    // 忽略无法转换的窗口
                }
            }

            return windows;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有顶级窗口时出错");
            return new List<Window>();
        }
    }

    /// <summary>
    /// 判断是否是对话框窗口
    /// </summary>
    /// <param name="window">窗口对象</param>
    /// <returns>是否是对话框</returns>
    private static bool IsDialogWindow(Window window)
    {
        try
        {
            // 常见的对话框类名
            var dialogClassNames = new[]
            {
                "#32770",           // 标准对话框
                "Dialog",           // 通用对话框
                "SHBrowseForFolder", // 文件夹浏览对话框
                "SHELLDLL_DefView", // Shell 对话框
                "FolderView"        // 文件夹视图
            };

            return dialogClassNames.Any(className =>
                window.ClassName.Contains(className, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 记录所有子窗口信息用于调试
    /// </summary>
    /// <param name="parentProcessId">父进程ID</param>
    private async Task LogAllChildWindowsAsync(int parentProcessId)
    {
        try
        {
            _logger.LogInformation("查找进程 {ProcessId} 相关的所有窗口:", parentProcessId);

            // 查找所有顶级窗口
            _logger.LogInformation("查找所有可能相关的顶级窗口:");
            var allWindows = GetAllTopLevelWindows();
            foreach (var window in allWindows.Take(20)) // 显示前20个
            {
                try
                {
                    var isRelevant = window.Title.Contains("文件夹", StringComparison.OrdinalIgnoreCase) ||
                                   window.Title.Contains("Folder", StringComparison.OrdinalIgnoreCase) ||
                                   window.Title.Contains("Browse", StringComparison.OrdinalIgnoreCase) ||
                                   window.Title.Contains("浏览", StringComparison.OrdinalIgnoreCase) ||
                                   window.ClassName.Contains("#32770") ||
                                   string.IsNullOrEmpty(window.Title);

                    if (isRelevant)
                    {
                        _logger.LogInformation("可能的对话框: Title='{Title}', ClassName='{ClassName}', Visible={Visible}",
                            window.Title, window.ClassName, !window.IsOffscreen);
                    }
                    else
                    {
                        _logger.LogDebug("其他窗口: Title='{Title}', ClassName='{ClassName}'",
                            window.Title, window.ClassName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "检查窗口时出错");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录子窗口信息时出错");
        }

        await Task.Delay(100); // 给日志一点时间输出
    }



    /// <summary>
    /// 解析 TestAllSpace 配置值
    /// </summary>
    /// <param name="value">配置值</param>
    /// <returns>元组：(是否测试全部空间, 测试大小MB)</returns>
    private (bool shouldTestAll, int testSizeMB) ParseTestAllSpaceValue(object value)
    {
        try
        {
            _logger.LogDebug("解析 TestAllSpace 配置值: {Value} (类型: {Type})", value, value?.GetType().Name);

            return value switch
            {
                // JsonElement 处理（JSON 反序列化后的类型）
                JsonElement jsonElement when jsonElement.ValueKind == JsonValueKind.True => (true, 0),
                JsonElement jsonElement when jsonElement.ValueKind == JsonValueKind.False => (false, 0),
                JsonElement jsonElement when jsonElement.ValueKind == JsonValueKind.Number && jsonElement.TryGetInt32(out var jsonInt) && jsonInt > 0 => (false, jsonInt),
                JsonElement jsonElement when jsonElement.ValueKind == JsonValueKind.String && jsonElement.GetString() is string jsonStr => ParseStringValue(jsonStr),

                // 布尔值处理
                bool boolValue => (boolValue, 0),

                // 字符串处理
                string strValue => ParseStringValue(strValue),

                // 数字处理
                int intValue when intValue > 0 => (false, intValue),
                long longValue when longValue > 0 => (false, (int)Math.Min(longValue, int.MaxValue)),
                double doubleValue when doubleValue > 0 => (false, (int)Math.Round(doubleValue)),

                // 默认情况
                _ => (true, 0)
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "解析 TestAllSpace 配置值失败，使用默认值 (测试全部空间)");
            return (true, 0);
        }
    }

    /// <summary>
    /// 解析字符串值
    /// </summary>
    /// <param name="strValue">字符串值</param>
    /// <returns>元组：(是否测试全部空间, 测试大小MB)</returns>
    private (bool shouldTestAll, int testSizeMB) ParseStringValue(string strValue)
    {
        if (string.IsNullOrWhiteSpace(strValue))
            return (true, 0);

        if (strValue.Equals("true", StringComparison.OrdinalIgnoreCase))
            return (true, 0);

        if (strValue.Equals("false", StringComparison.OrdinalIgnoreCase))
            return (false, 0);

        if (int.TryParse(strValue, out var parsedInt) && parsedInt > 0)
            return (false, parsedInt);

        // 默认情况
        return (true, 0);
    }

    /// <summary>
    /// 安全获取UI配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    private string GetUIConfigValue(string key, string defaultValue)
    {
        try
        {
            if (_configuration.UIConfig.TryGetValue(key, out var value))
            {
                // 处理不同类型的值
                return value switch
                {
                    string strValue => strValue,
                    int intValue => intValue.ToString(),
                    long longValue => longValue.ToString(),
                    double doubleValue => doubleValue.ToString(),
                    _ => value?.ToString() ?? defaultValue
                };
            }

            _logger.LogDebug("UI配置键 '{Key}' 不存在，使用默认值: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取UI配置值失败，键: {Key}，使用默认值: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                CleanupAsync().Wait(5000);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放H2TestController资源时出错: {Message}", ex.Message);
            }
        }
        base.Dispose();
        GC.SuppressFinalize(this);
    }
}
