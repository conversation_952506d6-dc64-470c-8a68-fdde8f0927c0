using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;

namespace MassStorageStableTestTool.Core.Services;

/// <summary>
/// 磁盘格式化服务实现
/// </summary>
public class DiskFormatService : IDiskFormatService
{
    private readonly ILogger<DiskFormatService> _logger;
    private readonly List<string> _supportedFileSystems = new() { "FAT32", "NTFS", "exFAT" };

    public DiskFormatService(ILogger<DiskFormatService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 格式化磁盘
    /// </summary>
    public async Task<FormatResult> FormatDriveAsync(
        string driveLetter,
        string? fileSystem = null,
        string? volumeLabel = null,
        bool quickFormat = true,
        CancellationToken cancellationToken = default,
        IProgress<ProgressEventArgs>? progress = null)
    {
        var result = new FormatResult();
        var startTime = DateTime.Now;

        try
        {
            // 验证参数
            if (!ValidateDriveLetter(driveLetter))
            {
                return FormatResult.CreateFailure($"无效的驱动器盘符: {driveLetter}");
            }

            // 获取驱动器当前信息
            var driveInfo = new System.IO.DriveInfo(driveLetter);
            if (!driveInfo.IsReady)
            {
                return FormatResult.CreateFailure($"驱动器 {driveLetter} 未就绪");
            }

            // 如果未指定文件系统，使用当前文件系统
            if (string.IsNullOrEmpty(fileSystem))
            {
                fileSystem = driveInfo.DriveFormat;
                _logger.LogInformation("未指定文件系统，使用当前文件系统: {FileSystem}", fileSystem);
            }

            // 如果未指定卷标，使用当前卷标
            if (string.IsNullOrEmpty(volumeLabel))
            {
                volumeLabel = driveInfo.VolumeLabel;
                _logger.LogInformation("未指定卷标，使用当前卷标: {VolumeLabel}", volumeLabel);
            }

            _logger.LogInformation("开始格式化驱动器 {DriveLetter}，文件系统: {FileSystem}，卷标: {VolumeLabel}",
                driveLetter, fileSystem, volumeLabel);

            result.AddLog($"开始格式化驱动器 {driveLetter}，文件系统: {fileSystem}，卷标: {volumeLabel}");

            if (!IsFileSystemSupported(fileSystem))
            {
                return FormatResult.CreateFailure($"不支持的文件系统: {fileSystem}");
            }

            // 检查驱动器是否可以格式化
            progress?.Report(new ProgressEventArgs { Progress = 10, Status = "检查驱动器..." });
            var (canFormat, issues) = await CheckDriveFormatabilityAsync(driveLetter);
            if (!canFormat)
            {
                return FormatResult.CreateFailure($"驱动器不能格式化: {string.Join(", ", issues)}");
            }

            // 执行格式化
            progress?.Report(new ProgressEventArgs { Progress = 20, Status = "正在格式化..." });
            
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                var formatSuccess = await FormatDriveWindowsAsync(driveLetter, fileSystem, volumeLabel, quickFormat, cancellationToken, progress);
                if (!formatSuccess.Success)
                {
                    return formatSuccess;
                }
            }
            else
            {
                return FormatResult.CreateFailure("当前只支持Windows系统的磁盘格式化");
            }

            // 验证格式化结果
            progress?.Report(new ProgressEventArgs { Progress = 90, Status = "验证格式化结果..." });
            await Task.Delay(2000, cancellationToken); // 等待系统刷新

            driveInfo = new System.IO.DriveInfo(driveLetter);
            if (!driveInfo.IsReady)
            {
                return FormatResult.CreateFailure("格式化完成但驱动器未就绪");
            }

            result.Success = true;
            result.FileSystem = driveInfo.DriveFormat;
            result.VolumeLabel = driveInfo.VolumeLabel;
            result.Duration = DateTime.Now - startTime;
            result.AddLog($"格式化完成，文件系统: {result.FileSystem}，卷标: {result.VolumeLabel}");

            progress?.Report(new ProgressEventArgs { Progress = 100, Status = "格式化完成" });

            _logger.LogInformation("驱动器 {DriveLetter} 格式化成功，耗时: {Duration}",
                driveLetter, result.Duration);

            return result;
        }
        catch (OperationCanceledException)
        {
            result.AddLog("格式化被取消");
            return FormatResult.CreateFailure("格式化被取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "格式化驱动器 {DriveLetter} 失败", driveLetter);
            result.AddLog($"格式化失败: {ex.Message}");
            return FormatResult.CreateFailure($"格式化失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 检查驱动器是否可以安全格式化
    /// </summary>
    public async Task<(bool CanFormat, List<string> Issues)> CheckDriveFormatabilityAsync(string driveLetter)
    {
        var issues = new List<string>();

        try
        {
            if (!ValidateDriveLetter(driveLetter))
            {
                issues.Add($"无效的驱动器盘符: {driveLetter}");
                return (false, issues);
            }

            var driveInfo = new System.IO.DriveInfo(driveLetter);
            
            if (!driveInfo.IsReady)
            {
                issues.Add("驱动器未就绪");
                return (false, issues);
            }

            // 检查是否为可移动驱动器
            if (driveInfo.DriveType != DriveType.Removable)
            {
                issues.Add($"驱动器类型为 {driveInfo.DriveType}，只允许格式化可移动驱动器");
                return (false, issues);
            }

            // 检查是否为系统驱动器
            var systemDrive = Path.GetPathRoot(Environment.SystemDirectory);
            if (string.Equals(driveLetter.TrimEnd('\\'), systemDrive?.TrimEnd('\\'), StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("不能格式化系统驱动器");
                return (false, issues);
            }

            // 检查驱动器是否被占用
            if (await IsDriveInUseAsync(driveLetter))
            {
                issues.Add("驱动器正在被其他程序使用");
                return (false, issues);
            }

            return (true, issues);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查驱动器 {DriveLetter} 可格式化性时出错", driveLetter);
            issues.Add($"检查驱动器时出错: {ex.Message}");
            return (false, issues);
        }
    }

    /// <summary>
    /// 获取支持的文件系统列表
    /// </summary>
    public List<string> GetSupportedFileSystems()
    {
        return new List<string>(_supportedFileSystems);
    }

    /// <summary>
    /// 验证文件系统类型是否受支持
    /// </summary>
    public bool IsFileSystemSupported(string fileSystem)
    {
        return _supportedFileSystems.Contains(fileSystem, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 在Windows系统上格式化驱动器
    /// </summary>
    private async Task<FormatResult> FormatDriveWindowsAsync(
        string driveLetter,
        string fileSystem,
        string volumeLabel,
        bool quickFormat,
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null)
    {
        try
        {
            // 使用format命令进行格式化
            var formatArgs = BuildFormatCommand(driveLetter, fileSystem, volumeLabel, quickFormat);
            
            _logger.LogDebug("执行格式化命令: {Command}", formatArgs);

            var processStartInfo = new ProcessStartInfo
            {
                FileName = "format",
                Arguments = formatArgs,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                RedirectStandardInput = true,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var process = new Process { StartInfo = processStartInfo };
            var output = new StringBuilder();
            var error = new StringBuilder();

            process.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    output.AppendLine(e.Data);
                    _logger.LogDebug("Format输出: {Output}", e.Data);
                    
                    // 尝试解析进度
                    if (e.Data.Contains("percent completed", StringComparison.OrdinalIgnoreCase))
                    {
                        var percentMatch = System.Text.RegularExpressions.Regex.Match(e.Data, @"(\d+)\s*percent");
                        if (percentMatch.Success && int.TryParse(percentMatch.Groups[1].Value, out var percent))
                        {
                            progress?.Report(new ProgressEventArgs 
                            { 
                                Progress = 20 + (percent * 60 / 100), 
                                Status = $"格式化进度: {percent}%" 
                            });
                        }
                    }
                }
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    error.AppendLine(e.Data);
                    _logger.LogWarning("Format错误: {Error}", e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            // 自动确认格式化（发送Y）
            await process.StandardInput.WriteLineAsync("Y");
            await process.StandardInput.FlushAsync();

            // 等待完成或取消
            while (!process.HasExited && !cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
            }

            if (cancellationToken.IsCancellationRequested)
            {
                process.Kill();
                return FormatResult.CreateFailure("格式化被取消");
            }

            await process.WaitForExitAsync(cancellationToken);

            if (process.ExitCode == 0)
            {
                return FormatResult.CreateSuccess(fileSystem, volumeLabel, TimeSpan.Zero);
            }
            else
            {
                var errorMessage = error.Length > 0 ? error.ToString() : "格式化失败";
                return FormatResult.CreateFailure($"格式化命令失败 (退出码: {process.ExitCode}): {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行Windows格式化命令时出错");
            return FormatResult.CreateFailure($"执行格式化命令时出错: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 构建格式化命令参数
    /// </summary>
    private string BuildFormatCommand(string driveLetter, string fileSystem, string volumeLabel, bool quickFormat)
    {
        var args = new StringBuilder();
        
        // 驱动器盘符
        args.Append($"{driveLetter.TrimEnd('\\', '/', ':')}: ");
        
        // 文件系统
        args.Append($"/FS:{fileSystem} ");
        
        // 卷标
        if (!string.IsNullOrWhiteSpace(volumeLabel))
        {
            args.Append($"/V:\"{volumeLabel}\" ");
        }
        
        // 快速格式化
        if (quickFormat)
        {
            args.Append("/Q ");
        }
        
        // 自动确认
        args.Append("/Y");
        
        return args.ToString();
    }

    /// <summary>
    /// 验证驱动器盘符格式
    /// </summary>
    private bool ValidateDriveLetter(string driveLetter)
    {
        if (string.IsNullOrWhiteSpace(driveLetter))
            return false;

        var cleanDrive = driveLetter.Trim().TrimEnd('\\', '/');
        if (!cleanDrive.EndsWith(':'))
            cleanDrive += ":";

        return cleanDrive.Length == 2 && char.IsLetter(cleanDrive[0]);
    }

    /// <summary>
    /// 检查驱动器是否正在被使用
    /// </summary>
    private async Task<bool> IsDriveInUseAsync(string driveLetter)
    {
        try
        {
            // 简单检查：尝试创建一个临时文件
            var testFile = Path.Combine(driveLetter, $"test_{Guid.NewGuid():N}.tmp");
            await File.WriteAllTextAsync(testFile, "test");
            File.Delete(testFile);
            return false;
        }
        catch
        {
            return true;
        }
    }
}
