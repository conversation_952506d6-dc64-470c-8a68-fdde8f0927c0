﻿using System.Diagnostics;
using System.Text;

Console.WriteLine("测试格式化命令执行...");

// 测试通过cmd.exe执行format命令（使用help参数，不会实际格式化）
await TestFormatCommandExecution();

Console.WriteLine("测试完成，按任意键退出...");
Console.ReadKey();

static async Task TestFormatCommandExecution()
{
    try
    {
        // 使用format /?来测试命令是否可用，不会实际格式化任何内容
        var processStartInfo = new ProcessStartInfo
        {
            FileName = "cmd.exe",
            Arguments = "/c format /?",
            UseShellExecute = false,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            CreateNoWindow = true,
            StandardOutputEncoding = Encoding.UTF8,
            StandardErrorEncoding = Encoding.UTF8
        };

        using var process = new Process { StartInfo = processStartInfo };
        var output = new StringBuilder();
        var error = new StringBuilder();

        process.OutputDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                output.AppendLine(e.Data);
                Console.WriteLine($"输出: {e.Data}");
            }
        };

        process.ErrorDataReceived += (sender, e) =>
        {
            if (!string.IsNullOrEmpty(e.Data))
            {
                error.AppendLine(e.Data);
                Console.WriteLine($"错误: {e.Data}");
            }
        };

        Console.WriteLine("启动format命令测试...");
        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();

        await process.WaitForExitAsync();

        Console.WriteLine($"命令执行完成，退出代码: {process.ExitCode}");

        if (process.ExitCode == 0)
        {
            Console.WriteLine("✅ format命令可以正常执行");
        }
        else
        {
            Console.WriteLine("❌ format命令执行失败");
            Console.WriteLine($"错误输出: {error}");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ 执行测试时出错: {ex.Message}");
        Console.WriteLine($"详细错误: {ex}");
    }
}
